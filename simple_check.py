import importlib.util
import sys

def check_module(name):
    found = importlib.util.find_spec(name) is not None
    print(f"{name}: {'✅ 已安装' if found else '❌ 未安装'}")
    return found

print("=== 内核包检查 ===")
pplx = check_module('pplx_kernels')
deep_ep = check_module('deep_ep')
deep_gemm = check_module('deep_gemm')

print("\n=== 结论 ===")
if deep_ep:
    print("✅ deep_ep 已安装，VLLM_ALL2ALL_BACKEND='deepep_low_latency' 应该可以工作")
else:
    print("❌ deep_ep 未安装，这就是为什么看到 'Using naive all2all manager.' 的原因")
    print("需要安装: git clone https://github.com/deepseek-ai/DeepEP && cd DeepEP && pip install -e .")
