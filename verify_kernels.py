#!/usr/bin/env python3

import importlib.util
import sys

def _has_module(module_name: str) -> bool:
    """Return True if *module_name* can be found in the current environment."""
    return importlib.util.find_spec(module_name) is not None

def has_pplx() -> bool:
    """Whether the optional `pplx_kernels` package is available."""
    return _has_module("pplx_kernels")

def has_deep_ep() -> bool:
    """Whether the optional `deep_ep` package is available."""
    return _has_module("deep_ep")

def has_deep_gemm() -> bool:
    """Whether the optional `deep_gemm` package is available."""
    return _has_module("deep_gemm")

def main():
    print("=== vLLM 可选内核包检查 ===\n")
    
    # 检查 pplx_kernels
    print("1. PPLX Kernels:")
    if has_pplx():
        print("   ✅ pplx_kernels 已安装")
        try:
            import pplx_kernels
            print(f"   📦 版本: {getattr(pplx_kernels, '__version__', '未知')}")
        except Exception as e:
            print(f"   ⚠️  导入时出错: {e}")
    else:
        print("   ❌ pplx_kernels 未安装")
    
    print()
    
    # 检查 deep_ep
    print("2. DeepEP Kernels:")
    if has_deep_ep():
        print("   ✅ deep_ep 已安装")
        try:
            import deep_ep
            print(f"   📦 版本: {getattr(deep_ep, '__version__', '未知')}")
        except Exception as e:
            print(f"   ⚠️  导入时出错: {e}")
    else:
        print("   ❌ deep_ep 未安装")
        print("   💡 安装方法:")
        print("      git clone https://github.com/deepseek-ai/DeepEP")
        print("      cd DeepEP && pip install -e .")
    
    print()
    
    # 检查 deep_gemm
    print("3. DeepGEMM Kernels:")
    if has_deep_gemm():
        print("   ✅ deep_gemm 已安装")
        try:
            import deep_gemm
            print(f"   📦 版本: {getattr(deep_gemm, '__version__', '未知')}")
        except Exception as e:
            print(f"   ⚠️  导入时出错: {e}")
    else:
        print("   ❌ deep_gemm 未安装")
        print("   💡 安装方法:")
        print("      git clone https://github.com/deepseek-ai/DeepGEMM")
        print("      cd DeepGEMM && pip install -e .")
    
    print()
    
    # 总结
    print("=== 总结 ===")
    all_kernels = {
        "pplx_kernels": has_pplx(),
        "deep_ep": has_deep_ep(),
        "deep_gemm": has_deep_gemm()
    }
    
    installed = [name for name, status in all_kernels.items() if status]
    missing = [name for name, status in all_kernels.items() if not status]
    
    if installed:
        print(f"✅ 已安装: {', '.join(installed)}")
    if missing:
        print(f"❌ 未安装: {', '.join(missing)}")
    
    print()
    
    # 针对您的问题的特别说明
    print("=== 关于 VLLM_ALL2ALL_BACKEND ===")
    if has_deep_ep():
        print("✅ deep_ep 已安装，VLLM_ALL2ALL_BACKEND='deepep_low_latency' 应该可以工作")
    else:
        print("❌ deep_ep 未安装，VLLM_ALL2ALL_BACKEND='deepep_low_latency' 会回退到 'naive'")
        print("   这就是为什么您看到 'Using naive all2all manager.' 的原因")

if __name__ == "__main__":
    main()
