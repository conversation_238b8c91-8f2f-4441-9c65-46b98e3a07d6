{"1": {"kernel_kind": "v1_2stages_tc", "best_config": {"stage1": {"BLOCK_N": 16, "num_stages": 1, "num_warps": 2, "num_ldmatrixes": 0}, "stage2": {"BLOCK_N": 16, "num_stages": 1, "num_warps": 8, "num_ldmatrixes": 0}}, "best_us": 87.52100169658661}, "100": {"kernel_kind": "v1_2stages_tc", "best_config": {"stage1": {"BLOCK_N": 32, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "stage2": {"BLOCK_N": 64, "num_stages": 1, "num_warps": 8, "num_ldmatrixes": 0}}, "best_us": 109.2820018529892}, "400": {"kernel_kind": "v1_2stages_tc", "best_config": {"stage1": {"BLOCK_N": 16, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "stage2": {"BLOCK_N": 32, "num_stages": 1, "num_warps": 8, "num_ldmatrixes": 0}}, "best_us": 179.92249131202698}, "700": {"kernel_kind": "v1_2stages_tc", "best_config": {"stage1": {"BLOCK_N": 16, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "stage2": {"BLOCK_N": 32, "num_stages": 1, "num_warps": 8, "num_ldmatrixes": 0}}, "best_us": 266.0830020904541}, "1000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 200.48299431800842}, "1300": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 32, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 228.32299768924713}, "1600": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 32, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 228.48299145698547}, "1900": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 2}, "stage2": {"num_stages": 1, "num_warps": 2}}, "best_us": 189.52250480651855}, "2200": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 2}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 194.48299705982208}, "2500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 197.84200191497803}, "2800": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 2}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 193.44200193881989}, "3100": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 2}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 189.28200006484985}, "3400": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 32, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 234.2430055141449}, "3700": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 2}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 258.80300998687744}, "4000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 2}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 274.40300583839417}, "4300": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 208.96300673484802}, "4600": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 213.60298991203308}, "4900": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 205.84198832511902}, "5000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 211.2025022506714}, "5500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 2}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 214.8820012807846}, "6000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 32, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 2}}, "best_us": 269.92300152778625}, "6500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 32, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 2}}, "best_us": 408.0055058002472}, "7000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 215.84299206733704}, "7500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 2}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 234.32299494743347}, "8000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 215.52199125289917}, "8500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 234.40299928188324}, "9000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 226.8030047416687}, "9500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 2}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 267.4434781074524}, "10000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 290.08299112319946}, "10500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 2}}, "best_us": 273.6029922962189}, "11000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 32, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 2}}, "best_us": 306.1639964580536}, "11500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 389.28499817848206}, "12000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 270.24298906326294}, "12500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 2}}, "best_us": 464.0049934387207}, "13000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 271.3640034198761}, "13500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 279.68400716781616}, "14000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 281.60300850868225}, "14500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 462.48602867126465}, "15000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 2}, "stage2": {"num_stages": 1, "num_warps": 2}}, "best_us": 383.3639919757843}, "15500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 2}}, "best_us": 469.36601400375366}, "16000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 2}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 287.8440022468567}, "16500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 295.12351751327515}, "17000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 2}}, "best_us": 445.4450011253357}, "17500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 2}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 299.5240092277527}, "18000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 305.28348684310913}, "18500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 301.44399404525757}, "19000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 307.4440062046051}, "19500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 309.6030056476593}, "20000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 2}, "stage2": {"num_stages": 1, "num_warps": 2}}, "best_us": 447.68598675727844}, "20500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 2}}, "best_us": 533.6059927940369}, "21000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 2}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 327.6839852333069}, "21500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 302.0839989185333}, "22000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 306.4830005168915}, "22500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 324.4040012359619}, "23000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 360.4849874973297}, "23500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 220.16200423240662}, "24000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 220.9630012512207}, "24500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 220.80199420452118}, "25000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 232.6429933309555}, "25500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 232.48299956321716}, "26000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 233.76299440860748}, "26500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 234.40299928188324}, "27000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 244.3230003118515}, "27500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 245.1229989528656}, "28000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 246.40299379825592}, "28500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 246.9629943370819}, "29000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 256.96301460266113}, "29500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 258.0829858779907}, "30000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 258.7229907512665}, "30500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 259.5230042934418}, "31000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 269.12298798561096}, "31500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 269.60399746894836}, "32000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 270.4029977321625}, "32500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 4}}, "best_us": 271.84298634529114}}