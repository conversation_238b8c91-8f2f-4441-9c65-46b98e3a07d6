{"1": {"kernel_kind": "v1_2stages_tc", "best_config": {"stage1": {"BLOCK_N": 16, "num_stages": 1, "num_warps": 2, "num_ldmatrixes": 0}, "stage2": {"BLOCK_N": 16, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}}, "best_us": 31.360000371932983}, "100": {"kernel_kind": "v1_2stages_tc", "best_config": {"stage1": {"BLOCK_N": 16, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "stage2": {"BLOCK_N": 64, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}}, "best_us": 48.79999905824661}, "400": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 78.5600021481514}, "700": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 84.63999629020691}, "1000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 87.99999952316284}, "1300": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 86.87999844551086}, "1600": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 89.75999802350998}, "1900": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 91.839998960495}, "2200": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 102.08000242710114}, "2500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 103.35999727249146}, "2800": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 104.80000078678131}, "3100": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 104.3199971318245}, "3400": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 105.43999820947647}, "3700": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 106.39999806880951}, "4000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 106.55999928712845}, "4300": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 119.03949826955795}, "4600": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 119.84000355005264}, "4900": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 119.35999989509583}, "5000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 118.56000125408173}, "5500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 120.4800009727478}, "6000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 121.11999839544296}, "6500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 133.27999413013458}, "7000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 134.39999520778656}, "7500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 134.88000631332397}, "8000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 135.04000008106232}, "8500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 146.55999839305878}, "9000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 146.55999839305878}, "9500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 148.00000190734863}, "10000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 148.3200043439865}, "10500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 159.9999964237213}, "11000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 159.9999964237213}, "11500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 160.64000129699707}, "12000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 162.08000481128693}, "12500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 173.6000031232834}, "13000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 175.99999904632568}, "13500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 175.20000040531158}, "14000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 175.35999417304993}, "14500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 186.71999871730804}, "15000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 186.71999871730804}, "15500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 187.83999979496002}, "16000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 186.88000738620758}, "16500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 199.0399956703186}, "17000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 199.8399943113327}, "17500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 200.00000298023224}, "18000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 200.95999538898468}, "18500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 211.84000372886658}, "19000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 213.76000344753265}, "19500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 213.919997215271}, "20000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 213.918998837471}, "20500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 225.11999309062958}, "21000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 225.9189933538437}, "21500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 225.43999552726746}, "22000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 226.23999416828156}, "22500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 224.31999444961548}, "23000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 238.87999355793}, "23500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 238.39999735355377}, "24000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 239.51999843120575}, "24500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 236.80000007152557}, "25000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 252.00000405311584}, "25500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 250.71999430656433}, "26000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 251.99949741363525}, "26500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 249.59999322891235}, "27000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 263.5200023651123}, "27500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 264.8000121116638}, "28000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 265.1199996471405}, "28500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 265.9189999103546}, "29000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 277.75999903678894}, "29500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 277.75898575782776}, "30000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 279.04000878334045}, "30500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 278.56001257896423}, "31000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 289.92000222206116}, "31500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 290.23998975753784}, "32000": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 289.6000146865845}, "32500": {"kernel_kind": "v2_tc", "best_config": {"stage1": {"BLOCK_N": 16, "BLOCK_DIM": 64, "num_stages": 1, "num_warps": 4}, "stage2": {"num_stages": 1, "num_warps": 8}}, "best_us": 291.03949666023254}}