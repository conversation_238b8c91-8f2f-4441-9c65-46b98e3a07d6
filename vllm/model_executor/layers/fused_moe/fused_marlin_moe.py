# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project
"""Fused MoE utilities for GPTQ."""
import functools
from typing import Optional

import torch
try:
    import marlin
except Exception:
    print("INFO: Please install marlin if you want to infer awq moe of marlin.\n") 
import vllm.envs as envs
import vllm._custom_ops as ops
from vllm.model_executor.layers.fused_moe.fused_moe import (
    moe_align_block_size, try_get_optimal_moe_config)
from vllm.model_executor.layers.quantization.utils.marlin_utils import (
    marlin_make_workspace_new, maybe_warn_marlin_atomic_add)
from vllm.scalar_type import ScalarType, scalar_types
from vllm.utils import direct_register_custom_op

def get_scalar_type(num_bits: int, has_zp: bool):
    if has_zp:
        return scalar_types.uint4 if num_bits == 4 else scalar_types.uint8
    else:
        return scalar_types.uint4b8 if num_bits == 4 else scalar_types.uint8b128

def fused_marlin_moe(
                     hidden_states: torch.Tensor, # 32, 7168 
                     w1: torch.Tensor, # 256, 512, 7168 --> 32*8, 512 --> 32*8, 256
                     w2: torch.Tensor, # 256, 256, 7168
                     w1_scale: torch.Tensor,
                     w2_scale: torch.Tensor,
                     gating_output: torch.Tensor,
                     topk_weights: torch.Tensor,
                     topk_ids: torch.Tensor,
                     global_num_experts: int = -1,
                     expert_map: Optional[torch.Tensor] = None,
                     g_idx1: Optional[torch.Tensor] = None,
                     g_idx2: Optional[torch.Tensor] = None,
                     sort_indices1: Optional[torch.Tensor] = None,
                     sort_indices2: Optional[torch.Tensor] = None,
                     w1_zeros: Optional[torch.Tensor] = None,
                     w2_zeros: Optional[torch.Tensor] = None,
                    #  workspace: Optional[torch.Tensor] = None,
                     num_bits: int = 4,
                     is_k_full: bool = True,
                     inplace: bool = False) -> torch.Tensor:
    """
    This function computes a Mixture of Experts (MoE) layer using two sets of
    weights, w1 and w2, and top-k gating mechanism.

    Parameters:
    - hidden_states (torch.Tensor): The input tensor to the MoE layer.
    - w1 (torch.Tensor): The first set of expert weights.
    - w2 (torch.Tensor): The second set of expert weights.
    - w1_scale (torch.Tensor): Scale to be used for w1.
    - w2_scale (torch.Tensor): Scale to be used for w2.
    - gating_output (torch.Tensor): The output of the gating operation
        (before softmax).
    - g_idx1 (Optional[torch.Tensor]): The first set of act_order indices.
    - g_idx2 (Optional[torch.Tensor]): The second set of act_order indices.
    - sort_indices1 (Optional[torch.Tensor]): The first act_order input
        permutation.
    - sort_indices2 (Optional[torch.Tensor]): The second act_order input
        permutation.
    - topk_weights (torch.Tensor): Top-k weights.
    - topk_ids (torch.Tensor): Indices of topk-k elements.
    - w1_zeros (Optional[torch.Tensor]): Optional zero points to be used for w1.
    - w2_zeros (Optional[torch.Tensor]): Optional zero points to be used for w2.
    - num_bits (bool): The number of bits in expert weights quantization.

    Returns:
    - torch.Tensor: The output tensor after applying the MoE layer.
    """
    # quant_type = ScalarType.from_id(quant_type_id)
    # assert quant_type in [
    #     scalar_types.uint4, scalar_types.uint8b128, scalar_types.uint4b8,
    #     scalar_types.float8_e4m3fn, scalar_types.float4_e2m1f
    # ]

    # bit4_scalar_types = [
    #     scalar_types.uint4, scalar_types.uint4b8, scalar_types.float4_e2m1f
    # ]
    # num_bits = 4 if quant_type in bit4_scalar_types else 8

    # Check constraints.
    assert hidden_states.shape[0] == gating_output.shape[
        0], "Number of tokens mismatch"
    assert hidden_states.shape[
        1] == w1.shape[1] * 16, "Hidden size mismatch w1"
    assert hidden_states.shape[1] == w2.shape[2] // (
        num_bits // 2), "Hidden size mismatch w2"
    assert hidden_states.is_contiguous(), "Hidden_states must be contiguous"
    assert w1.is_contiguous(), "Expert weights1 must be contiguous"
    assert w2.is_contiguous(), "Expert weights2 must be contiguous"
    assert hidden_states.dtype in [torch.float16, torch.bfloat16]
    # assert num_bits in [4, 8]
    # 目前只支持 uint4的量化结果
    assert num_bits in [4]

    M, K = hidden_states.shape # 32, 7168
    E = w1.shape[0] # 256
    N = w2.shape[1] * 16 # 256
    topk = topk_ids.shape[1] # 8
    # # 计算 topk_weights 和 topk_ids
    # topk_weights, topk_ids = fused_topk(hidden_states, score, topk, False)

    # 选择 block_size_m 的逻辑按照 Marlin来设置
    for block_size_m in [16, 32, 48, 64, 80]:
        if M * topk / E / block_size_m < 0.9:
            break
    # print("m: ", M, "; block_m: ", block_size_m)

    if global_num_experts == -1:
        global_num_experts = E
    sorted_token_ids, expert_ids, num_tokens_post_padded = \
        moe_align_block_size(topk_ids, block_size_m, global_num_experts,
                             expert_map)
    # max_num = num_tokens_post_padded.item()
    # print("max_num: ", max_num)
    # 输出 
    # for i in range(0, max_num, block_size_m):
    #     print(i / block_size_m, sorted_token_ids[i:(i + block_size_m)])
    # if workspace is None:
    #     max_workspace_size = (max(2 * N, K) // 64) * \
    #         (sorted_token_ids.size(0) // block_size_m)
    #     device = hidden_states.device
    #     sms = torch.cuda.get_device_properties(device).multi_processor_count
    #     max_workspace_size = min(max_workspace_size, sms * 4)
    #     workspace = torch.zeros(max_workspace_size,
    #                             dtype=torch.int,
    #                             device=device,
    #                             requires_grad=False)

    scalar_type1 = get_scalar_type(num_bits, w1_zeros is not None)
    scalar_type2 = get_scalar_type(num_bits, w2_zeros is not None)

    intermediate_cache2 = torch.empty(  # [32*8, 256]
        (M * topk_ids.shape[1], N),
        device=hidden_states.device,
        dtype=hidden_states.dtype,
    )
    intermediate_cache13 = torch.empty(
        (M * topk_ids.shape[1] * max(2 * N, K), ),
        device=hidden_states.device,
        dtype=hidden_states.dtype,
    )
    intermediate_cache1 = intermediate_cache13[:M * topk_ids.shape[1] * 2 * N] # [32*8, 512]
    intermediate_cache1 = intermediate_cache1.view(-1, 2 * N)
    intermediate_cache3 = intermediate_cache13[:M * topk_ids.shape[1] * K] # # [32*8, 7168]
    intermediate_cache3 = intermediate_cache3.view(-1, K) 

    use_atomic_add = hidden_states.dtype == torch.half or \
        torch.cuda.get_device_capability(hidden_states.device)[0] >= 9

    intermediate_cache1.zero_()
    intermediate_cache1 = torch.ops.marlin.moe_wna16_marlin_gemm(
        hidden_states, # [32, 7168] # arg0: torch.Tensor,
        intermediate_cache1, # [32*8, 512] # arg1: Optional[torch.Tensor]
        w1, # arg2: torch.Tensor
        w1_scale, #  arg3: torch.Tensor
        # w1_zeros, #  arg4: Optional[torch.Tensor]
        g_idx1, # arg5: Optional[torch.Tensor]
        sort_indices1, # arg6: Optional[torch.Tensor]
        # workspace, # arg7: torch.Tensor
        sorted_token_ids, # arg8: torch.Tensor
        expert_ids, # arg9: torch.Tensor
        num_tokens_post_padded, # arg10: torch.Tensor
        topk_weights, #arg11: torch.Tensor, 
        block_size_m,#  arg12: int,
        topk, # arg13: int,
        False, # arg14: bool,
        expert_map is not None, #  arg15: bool,
        scalar_type1.id, # arg16: int
        M, # arg17: int, 
        2 * N, # arg18: int
        K, # arg19: int,
        is_k_full, # arg20: bool,
        use_atomic_add, # arg21: bool,
        True, # arg22: bool
        False
        ) #  arg23: bool
    
    # [32*8, 512] -->  [32*8, 256]
    torch.ops._C.silu_and_mul(intermediate_cache2,
                              intermediate_cache1.view(-1, 2 * N))
    intermediate_cache3.zero_()
    intermediate_cache3 = torch.ops.marlin.moe_wna16_marlin_gemm(
        intermediate_cache2, # [32*8, 256]
        intermediate_cache3, # [32*8, 7168]
        w2,
        w2_scale,
        # w2_zeros,
        g_idx2,
        sort_indices2,
        # workspace,
        sorted_token_ids,
        expert_ids,
        num_tokens_post_padded,
        topk_weights,
        block_size_m,
        1,
        True,
        expert_map is not None,
        scalar_type2.id,
        M * topk,
        K,
        N,
        is_k_full,
        use_atomic_add,
        True,
        False
        ).view(-1, topk, K)
    
    output = hidden_states if inplace else torch.empty_like(hidden_states)
    # return torch.sum(intermediate_cache3.view(*intermediate_cache3.shape),
    #                  dim=1,
    #                  out=output)
    ops.moe_sum(intermediate_cache3.view(*intermediate_cache3.shape), output)   
    return output

def fused_marlin_moe_fake(
                     hidden_states: torch.Tensor, # 32, 7168 
                     w1: torch.Tensor, # 256, 512, 7168 --> 32*8, 512 --> 32*8, 256
                     w2: torch.Tensor, # 256, 256, 7168
                     w1_scale: torch.Tensor,
                     w2_scale: torch.Tensor,
                     gating_output: torch.Tensor,
                     topk_weights: torch.Tensor,
                     topk_ids: torch.Tensor,
                     global_num_experts: int = -1,
                     expert_map: Optional[torch.Tensor] = None,
                     g_idx1: Optional[torch.Tensor] = None,
                     g_idx2: Optional[torch.Tensor] = None,
                     sort_indices1: Optional[torch.Tensor] = None,
                     sort_indices2: Optional[torch.Tensor] = None,
                     w1_zeros: Optional[torch.Tensor] = None,
                     w2_zeros: Optional[torch.Tensor] = None,
                    #  workspace: Optional[torch.Tensor] = None,
                     num_bits: int = 4,
                     is_k_full: bool = True,
                     inplace: bool = False) -> torch.Tensor:
    return torch.empty_like(hidden_states)


direct_register_custom_op(
    op_name="fused_marlin_moe",
    op_func=fused_marlin_moe,
    mutates_args=[],
    fake_impl=fused_marlin_moe_fake,
)
