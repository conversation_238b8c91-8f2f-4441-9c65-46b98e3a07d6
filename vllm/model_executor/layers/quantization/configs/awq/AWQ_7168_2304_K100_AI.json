{"7168_2304": {"1": {"BLOCK_SIZE_M": 16, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 32, "GROUP_SIZE_M": 8, "SPLIT_K": 8, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "2": {"BLOCK_SIZE_M": 16, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 32, "GROUP_SIZE_M": 4, "SPLIT_K": 8, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "3": {"BLOCK_SIZE_M": 16, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 32, "GROUP_SIZE_M": 4, "SPLIT_K": 8, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "4": {"BLOCK_SIZE_M": 16, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 32, "GROUP_SIZE_M": 8, "SPLIT_K": 8, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "5": {"BLOCK_SIZE_M": 16, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 32, "GROUP_SIZE_M": 4, "SPLIT_K": 8, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "6": {"BLOCK_SIZE_M": 16, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 32, "GROUP_SIZE_M": 4, "SPLIT_K": 8, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "7": {"BLOCK_SIZE_M": 16, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 32, "GROUP_SIZE_M": 8, "SPLIT_K": 8, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "8": {"BLOCK_SIZE_M": 16, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 32, "GROUP_SIZE_M": 4, "SPLIT_K": 8, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "9": {"BLOCK_SIZE_M": 16, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 32, "GROUP_SIZE_M": 8, "SPLIT_K": 8, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "10": {"BLOCK_SIZE_M": 16, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 32, "GROUP_SIZE_M": 4, "SPLIT_K": 8, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "11": {"BLOCK_SIZE_M": 16, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 32, "GROUP_SIZE_M": 4, "SPLIT_K": 8, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "12": {"BLOCK_SIZE_M": 16, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 32, "GROUP_SIZE_M": 8, "SPLIT_K": 8, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "13": {"BLOCK_SIZE_M": 16, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 32, "GROUP_SIZE_M": 8, "SPLIT_K": 8, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "14": {"BLOCK_SIZE_M": 16, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 32, "GROUP_SIZE_M": 4, "SPLIT_K": 8, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "15": {"BLOCK_SIZE_M": 16, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 32, "GROUP_SIZE_M": 4, "SPLIT_K": 8, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "16": {"BLOCK_SIZE_M": 16, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 32, "GROUP_SIZE_M": 8, "SPLIT_K": 8, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 0}, "32": {"BLOCK_SIZE_M": 32, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 64, "GROUP_SIZE_M": 4, "SPLIT_K": 4, "num_stages": 1, "num_warps": 8, "num_ldmatrixes": 0}, "64": {"BLOCK_SIZE_M": 64, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 64, "GROUP_SIZE_M": 8, "SPLIT_K": 2, "num_stages": 0, "num_warps": 8, "num_ldmatrixes": 1}, "128": {"BLOCK_SIZE_M": 64, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 64, "GROUP_SIZE_M": 4, "SPLIT_K": 1, "num_stages": 0, "num_warps": 8, "num_ldmatrixes": 1}, "256": {"BLOCK_SIZE_M": 128, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 64, "GROUP_SIZE_M": 8, "SPLIT_K": 1, "num_stages": 0, "num_warps": 8, "num_ldmatrixes": 1}, "512": {"BLOCK_SIZE_M": 128, "BLOCK_SIZE_N": 128, "BLOCK_SIZE_K": 64, "GROUP_SIZE_M": 4, "SPLIT_K": 1, "num_stages": 1, "num_warps": 8, "num_ldmatrixes": 1}, "1024": {"BLOCK_SIZE_M": 128, "BLOCK_SIZE_N": 64, "BLOCK_SIZE_K": 64, "GROUP_SIZE_M": 4, "SPLIT_K": 1, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 1}, "2048": {"BLOCK_SIZE_M": 128, "BLOCK_SIZE_N": 128, "BLOCK_SIZE_K": 64, "GROUP_SIZE_M": 8, "SPLIT_K": 1, "num_stages": 1, "num_warps": 8, "num_ldmatrixes": 1}, "4096": {"BLOCK_SIZE_M": 128, "BLOCK_SIZE_N": 128, "BLOCK_SIZE_K": 32, "GROUP_SIZE_M": 8, "SPLIT_K": 1, "num_stages": 1, "num_warps": 4, "num_ldmatrixes": 1}}}